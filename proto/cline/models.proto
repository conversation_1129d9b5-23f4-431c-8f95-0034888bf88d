syntax = "proto3";

package cline;
import "cline/common.proto";
option java_package = "bot.cline.proto";
option java_multiple_files = true;

// Service for model-related operations
service ModelsService {
  // Fetches available models from Ollama
  rpc getOllamaModels(StringRequest) returns (StringArray);
  // Fetches available models from LM Studio
  rpc getLmStudioModels(StringRequest) returns (StringArray);
  // Fetches available models from VS Code LM API
  rpc getVsCodeLmModels(EmptyRequest) returns (VsCodeLmModelsArray);
  // Refreshes and returns OpenRouter models
  rpc refreshOpenRouterModels(EmptyRequest) returns (OpenRouterCompatibleModelInfo);
  // Refreshes and returns Hugging Face models
  rpc refreshHuggingFaceModels(EmptyRequest) returns (OpenRouterCompatibleModelInfo);
  // Refreshes and returns OpenAI models
  rpc refreshOpenAiModels(OpenAiModelsRequest) returns (StringArray);
  // Refreshes and returns Requesty models
  rpc refreshRequestyModels(EmptyRequest) returns (OpenRouterCompatibleModelInfo);
  // Subscribe to OpenRouter models updates
  rpc subscribeToOpenRouterModels(EmptyRequest) returns (stream OpenRouterCompatibleModelInfo);
  // Updates API configuration
  rpc updateApiConfigurationProto(UpdateApiConfigurationRequest) returns (Empty);
  // Refreshes and returns Groq models
  rpc refreshGroqModels(EmptyRequest) returns (OpenRouterCompatibleModelInfo);
  // Refreshes and returns Baseten models
  rpc refreshBasetenModels(EmptyRequest) returns (OpenRouterCompatibleModelInfo);
}

// List of VS Code LM models
message VsCodeLmModelsArray {
  repeated LanguageModelChatSelector models = 1;
}

// Structure representing a language model chat selector
message LanguageModelChatSelector {
  optional string vendor = 1;
  optional string family = 2;
  optional string version = 3;
  optional string id = 4;
}

// Price tier for tiered pricing models
message PriceTier {
  int32 token_limit = 1;  // Upper limit (inclusive) of input tokens for this price
  double price = 2;       // Price per million tokens for this tier
}

// Thinking configuration for models that support thinking/reasoning
message ThinkingConfig {
  optional int32 max_budget = 1;                    // Max allowed thinking budget tokens
  optional double output_price = 2;                 // Output price per million tokens when budget > 0
  repeated PriceTier output_price_tiers = 3;        // Optional: Tiered output price when budget > 0
}

// Model tier for tiered pricing structures
message ModelTier {
  int32 context_window = 1;
  optional double input_price = 2;
  optional double output_price = 3;
  optional double cache_writes_price = 4;
  optional double cache_reads_price = 5;
}

// For OpenRouterCompatibleModelInfo structure in OpenRouterModels
message OpenRouterModelInfo {
  optional int32 max_tokens = 1;
  optional int32 context_window = 2;
  optional bool supports_images = 3;
  bool supports_prompt_cache = 4;
  optional double input_price = 5;
  optional double output_price = 6;
  optional double cache_writes_price = 7;
  optional double cache_reads_price = 8;
  optional string description = 9;
  optional ThinkingConfig thinking_config = 10;
  optional bool supports_global_endpoint = 11;
  repeated ModelTier tiers = 12;
}

// Shared response message for model information
message OpenRouterCompatibleModelInfo {
  map<string, OpenRouterModelInfo> models = 1;
}

// Request for fetching OpenAI models
message OpenAiModelsRequest {
  Metadata metadata = 1;
  string base_url = 2;
  string api_key = 3;
}

// Request for updating API configuration
message UpdateApiConfigurationRequest {
  Metadata metadata = 1;
  ModelsApiConfiguration api_configuration = 2;
}

// API Provider enumeration
enum ApiProvider {
  ANTHROPIC = 0;
  OPENROUTER = 1;
  BEDROCK = 2;
  VERTEX = 3;
  OPENAI = 4;
  OLLAMA = 5;
  LMSTUDIO = 6;
  GEMINI = 7;
  OPENAI_NATIVE = 8;
  REQUESTY = 9;
  TOGETHER = 10;
  DEEPSEEK = 11;
  QWEN = 12;
  DOUBAO = 13;
  MISTRAL = 14;
  VSCODE_LM = 15;
  CLINE = 16;
  LITELLM = 17;
  NEBIUS = 18;
  FIREWORKS = 19;
  ASKSAGE = 20;
  XAI = 21;
  SAMBANOVA = 22;
  CEREBRAS = 23;
  GROQ = 24;
  SAPAICORE = 25;
  CLAUDE_CODE = 26;
  MOONSHOT = 27;
  HUGGINGFACE = 28;
  HUAWEI_CLOUD_MAAS = 29;
  BASETEN = 30;
}

// Model info for OpenAI-compatible models
message OpenAiCompatibleModelInfo {
  optional int32 max_tokens = 1;
  optional int32 context_window = 2;
  optional bool supports_images = 3;
  bool supports_prompt_cache = 4;
  optional double input_price = 5;
  optional double output_price = 6;
  optional ThinkingConfig thinking_config = 7;
  optional bool supports_global_endpoint = 8;
  optional double cache_writes_price = 9;
  optional double cache_reads_price = 10;
  optional string description = 11;
  repeated ModelTier tiers = 12;
  optional double temperature = 13;
  optional bool is_r1_format_required = 14;
}

// Model info for LiteLLM models
message LiteLLMModelInfo {
  optional int32 max_tokens = 1;
  optional int32 context_window = 2;
  optional bool supports_images = 3;
  bool supports_prompt_cache = 4;
  optional double input_price = 5;
  optional double output_price = 6;
  optional ThinkingConfig thinking_config = 7;
  optional bool supports_global_endpoint = 8;
  optional double cache_writes_price = 9;
  optional double cache_reads_price = 10;
  optional string description = 11;
  repeated ModelTier tiers = 12;
  optional double temperature = 13;
}

// Main ApiConfiguration message
message ModelsApiConfiguration {
  // Global configuration fields (not mode-specific)
  optional string api_key = 1;
  optional string cline_api_key = 2;
  optional string ulid = 3;
  optional string lite_llm_base_url = 4;
  optional string lite_llm_api_key = 5;
  optional bool lite_llm_use_prompt_cache = 6;
  map<string, string> open_ai_headers = 7;
  optional string anthropic_base_url = 8;
  optional string open_router_api_key = 9;
  optional string open_router_provider_sorting = 10;
  optional string aws_access_key = 11;
  optional string aws_secret_key = 12;
  optional string aws_session_token = 13;
  optional string aws_region = 14;
  optional bool aws_use_cross_region_inference = 15;
  optional bool aws_bedrock_use_prompt_cache = 16;
  optional bool aws_use_profile = 17;
  optional string aws_profile = 18;
  optional string aws_bedrock_endpoint = 19;
  optional string claude_code_path = 20;
  optional string vertex_project_id = 21;
  optional string vertex_region = 22;
  optional string open_ai_base_url = 23;
  optional string open_ai_api_key = 24;
  optional string ollama_base_url = 25;
  optional string ollama_api_options_ctx_num = 26;
  optional string lm_studio_base_url = 27;
  optional string gemini_api_key = 28;
  optional string gemini_base_url = 29;
  optional string open_ai_native_api_key = 30;
  optional string deep_seek_api_key = 31;
  optional string requesty_api_key = 32;
  optional string requesty_base_url = 33;
  optional string together_api_key = 34;
  optional string fireworks_api_key = 35;
  optional int32 fireworks_model_max_completion_tokens = 36;
  optional int32 fireworks_model_max_tokens = 37;
  optional string qwen_api_key = 38;
  optional string doubao_api_key = 39;
  optional string mistral_api_key = 40;
  optional string azure_api_version = 41;
  optional string qwen_api_line = 42;
  optional string nebius_api_key = 43;
  optional string asksage_api_url = 44;
  optional string asksage_api_key = 45;
  optional string xai_api_key = 46;
  optional string sambanova_api_key = 47;
  optional string cerebras_api_key = 48;
  optional int32 request_timeout_ms = 49;
  optional string sap_ai_core_client_id = 50;
  optional string sap_ai_core_client_secret = 51;
  optional string sap_ai_resource_group = 52;
  optional string sap_ai_core_token_url = 53;
  optional string sap_ai_core_base_url = 54;
  optional string moonshot_api_key = 55;
  optional string moonshot_api_line = 56;
  optional string aws_authentication = 57;
  optional string aws_bedrock_api_key = 58;
  optional string cline_account_id = 59;
  optional string groq_api_key = 60;
  optional string hugging_face_api_key = 61;
  optional string huawei_cloud_maas_api_key = 62;
  optional string baseten_api_key = 63;
  optional string ollama_api_key = 64;

  // Plan mode configurations
  optional ApiProvider plan_mode_api_provider = 100;
  optional string plan_mode_api_model_id = 101;
  optional int32 plan_mode_thinking_budget_tokens = 102;
  optional string plan_mode_reasoning_effort = 103;
  optional LanguageModelChatSelector plan_mode_vs_code_lm_model_selector = 104;
  optional bool plan_mode_aws_bedrock_custom_selected = 105;
  optional string plan_mode_aws_bedrock_custom_model_base_id = 106;
  optional string plan_mode_open_router_model_id = 107;
  optional OpenRouterModelInfo plan_mode_open_router_model_info = 108;
  optional string plan_mode_open_ai_model_id = 109;
  optional OpenAiCompatibleModelInfo plan_mode_open_ai_model_info = 110;
  optional string plan_mode_ollama_model_id = 111;
  optional string plan_mode_lm_studio_model_id = 112;
  optional string plan_mode_lite_llm_model_id = 113;
  optional LiteLLMModelInfo plan_mode_lite_llm_model_info = 114;
  optional string plan_mode_requesty_model_id = 115;
  optional OpenRouterModelInfo plan_mode_requesty_model_info = 116;
  optional string plan_mode_together_model_id = 117;
  optional string plan_mode_fireworks_model_id = 118;
  optional string plan_mode_sap_ai_core_model_id = 119;
  optional string plan_mode_groq_model_id = 120;
  optional OpenRouterModelInfo plan_mode_groq_model_info = 121;
  optional string plan_mode_hugging_face_model_id = 122;
  optional OpenRouterModelInfo plan_mode_hugging_face_model_info = 123;
  optional string plan_mode_huawei_cloud_maas_model_id = 124;
  optional OpenRouterModelInfo plan_mode_huawei_cloud_maas_model_info = 125; 
  optional string plan_mode_baseten_model_id = 126;
  optional OpenRouterModelInfo plan_mode_baseten_model_info = 127;

  // Act mode configurations
  optional ApiProvider act_mode_api_provider = 200;
  optional string act_mode_api_model_id = 201;
  optional int32 act_mode_thinking_budget_tokens = 202;
  optional string act_mode_reasoning_effort = 203;
  optional LanguageModelChatSelector act_mode_vs_code_lm_model_selector = 204;
  optional bool act_mode_aws_bedrock_custom_selected = 205;
  optional string act_mode_aws_bedrock_custom_model_base_id = 206;
  optional string act_mode_open_router_model_id = 207;
  optional OpenRouterModelInfo act_mode_open_router_model_info = 208;
  optional string act_mode_open_ai_model_id = 209;
  optional OpenAiCompatibleModelInfo act_mode_open_ai_model_info = 210;
  optional string act_mode_ollama_model_id = 211;
  optional string act_mode_lm_studio_model_id = 212;
  optional string act_mode_lite_llm_model_id = 213;
  optional LiteLLMModelInfo act_mode_lite_llm_model_info = 214;
  optional string act_mode_requesty_model_id = 215;
  optional OpenRouterModelInfo act_mode_requesty_model_info = 216;
  optional string act_mode_together_model_id = 217;
  optional string act_mode_fireworks_model_id = 218;
  optional string act_mode_sap_ai_core_model_id = 219;
  optional string act_mode_groq_model_id = 220;
  optional OpenRouterModelInfo act_mode_groq_model_info = 221;
  optional string act_mode_hugging_face_model_id = 222;
  optional OpenRouterModelInfo act_mode_hugging_face_model_info = 223;
  optional string act_mode_huawei_cloud_maas_model_id = 224;
  optional OpenRouterModelInfo act_mode_huawei_cloud_maas_model_info = 225;
  optional string act_mode_baseten_model_id = 226;
  optional OpenRouterModelInfo act_mode_baseten_model_info = 227;

  repeated string favorited_model_ids = 300;
}
