syntax = "proto3";

package host;
option java_package = "bot.cline.host.proto";
option java_multiple_files = true;

import "cline/common.proto";

// Provides methods for working with the user's environment.
service EnvService {
  // Writes text to the system clipboard.
  rpc clipboardWriteText(cline.StringRequest) returns (cline.Empty);

  // Reads text from the system clipboard.
  rpc clipboardReadText(cline.EmptyRequest) returns (cline.String);

  // Returns a stable machine identifier for telemetry distinctId purposes.
  rpc getMachineId(cline.EmptyRequest) returns (cline.String);

  rpc getHostVersion(cline.EmptyRequest) returns (GetHostVersionResponse);
}

message GetHostVersionResponse {
  // The name of the host platform, e.g VSCode
  optional string platform = 1; 
  // The version of the host platform, e.g. 1.103.0
  optional string version = 2;
}
