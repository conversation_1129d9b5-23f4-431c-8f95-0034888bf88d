syntax = "proto3";

package host;
option java_package = "bot.cline.host.proto";
option java_multiple_files = true;

import "cline/common.proto";

// Provides methods for diff views.
service DiffService {
  // Open the diff view/editor.
  rpc openDiff(OpenDiffRequest) returns (OpenDiffResponse);

  // Get the contents of the diff view.
  rpc getDocumentText(GetDocumentTextRequest) returns (GetDocumentTextResponse);

  // Replace a text selection in the diff.
  rpc replaceText(ReplaceTextRequest) returns (ReplaceTextResponse);

  rpc scrollDiff(ScrollDiffRequest) returns (ScrollDiffResponse);

  // Truncate the diff document.
  rpc truncateDocument(TruncateDocumentRequest) returns (TruncateDocumentResponse);

  // Save the diff document.
  rpc saveDocument(SaveDocumentRequest) returns (SaveDocumentResponse);

  // Close all the diff editor windows/tabs.
  // Any diff editors with unsaved content should not be closed.
  rpc closeAllDiffs(CloseAllDiffsRequest) returns (CloseAllDiffsResponse);

  // Display a diff view comparing before/after states for multiple files.
  // Content is passed as in-memory data, not read from the file system.
  rpc openMultiFileDiff(OpenMultiFileDiffRequest) returns (OpenMultiFileDiffResponse);
}

message OpenDiffRequest {
  optional cline.Metadata metadata = 1;
  // The absolute path of the document being edited.
  optional string path = 2;
  // The new content for the file.
  optional string content = 3;
}

message OpenDiffResponse {
  // A unique identifier for the diff view that was opened.
  optional string diff_id = 1;
}

message GetDocumentTextRequest {
  optional cline.Metadata metadata = 1;
  optional string diff_id = 2;
}

message GetDocumentTextResponse {
    optional string content = 1;
}

message ReplaceTextRequest {
  optional cline.Metadata metadata = 1;
  optional string diff_id = 2;
  optional string content = 3;
  optional int32 start_line = 4;
  optional int32 end_line = 5;
}

message ReplaceTextResponse {}

message ScrollDiffRequest {
  optional string diff_id = 1;
  optional int32 line = 2;
}

message ScrollDiffResponse {}

message TruncateDocumentRequest {
  optional cline.Metadata metadata = 1;
  optional string diff_id = 2;
  optional int32 end_line = 3;
}

message TruncateDocumentResponse {}

message CloseAllDiffsRequest {}

message CloseAllDiffsResponse {}

message SaveDocumentRequest {
  optional cline.Metadata metadata = 1;
  optional string diff_id = 2;
}

message SaveDocumentResponse {}

message OpenMultiFileDiffRequest {
  optional string title = 1;
  repeated ContentDiff diffs = 2;
}

message ContentDiff {
  // The absolute file path.
  optional string file_path = 1;
  optional string left_content = 2;
  optional string right_content = 3;
}

message OpenMultiFileDiffResponse {}
