syntax = "proto3";

package host;
option java_package = "bot.cline.host.proto";
option java_multiple_files = true;

import "cline/common.proto";

// Provides methods for working with IDE windows and editors.
service WindowService {
  // Opens a text document in the IDE editor and returns editor information.
  rpc showTextDocument(ShowTextDocumentRequest) returns (TextEditorInfo);

  // Shows the open file dialogue / file picker.
  rpc showOpenDialogue(ShowOpenDialogueRequest) returns (SelectedResources);

  // Shows a notification.
  rpc showMessage(ShowMessageRequest) returns (SelectedResponse);

  // Prompts the user for input and returns the response.
  rpc showInputBox(ShowInputBoxRequest) returns (ShowInputBoxResponse);

  // Shows the file save dialogue / file picker.
  rpc showSaveDialog(ShowSaveDialogRequest) returns (ShowSaveDialogResponse);

  // Opens a file in the IDE.
  rpc openFile(OpenFileRequest) returns (OpenFileResponse);

  // Returns the open tabs.
  rpc getOpenTabs(GetOpenTabsRequest) returns (GetOpenTabsResponse);

  // Returns the visible tabs.
  rpc getVisibleTabs(GetVisibleTabsRequest) returns (GetVisibleTabsResponse);
}

message ShowTextDocumentRequest {
  cline.Metadata metadata = 1;
  string path = 2;
  optional ShowTextDocumentOptions options = 3;
}

// See https://code.visualstudio.com/api/references/vscode-api#TextDocumentShowOptions
message ShowTextDocumentOptions {
  optional bool preview = 1;
  optional bool preserve_focus = 2;
  optional int32 view_column = 3;
}

message TextEditorInfo {
  string document_path = 1;
  optional int32 view_column = 2;
  bool is_active = 3;
}

message ShowOpenDialogueRequest {
  cline.Metadata metadata = 1;
  optional bool can_select_many = 2;
  optional string open_label = 3;
  optional ShowOpenDialogueFilterOption filters = 4;
}

message ShowOpenDialogueFilterOption {
  repeated string files = 1;
}

message SelectedResources {
  repeated string paths = 1;
}

enum ShowMessageType {
  ERROR = 0;
  INFORMATION = 1;
  WARNING = 2;
}

message ShowMessageRequest {
  ShowMessageType type = 1;
  string message = 2;
  optional ShowMessageRequestOptions options = 3;
}

message ShowMessageRequestOptions {
  repeated string items = 1;
  optional bool modal = 2;
  optional string detail = 3;

}

message SelectedResponse {
  optional string selected_option = 1;
}

message ShowSaveDialogRequest {
  optional ShowSaveDialogOptions options = 1;
}

message ShowSaveDialogOptions {
  optional string default_path = 1;
  // A map of file types to extensions, e.g
  // "Text Files": { "extensions": ["txt", "md"] }
  map<string, FileExtensionList> filters = 2;
}

message FileExtensionList {
  // A list of file extension (without the dot).
  repeated string extensions = 1;
}

message ShowSaveDialogResponse {
  // If the user cancelled the dialog, this will be empty.
  optional string selected_path = 1;
}

message ShowInputBoxRequest {
  string title = 1;
  optional string prompt = 2;
  optional string value = 3;
}

message ShowInputBoxResponse {
  optional string response = 1;
}

message OpenFileRequest {
  string file_path = 1;
}

message OpenFileResponse {}

message GetOpenTabsRequest {}

message GetOpenTabsResponse {
  repeated string paths = 1;
}

message GetVisibleTabsRequest {}

message GetVisibleTabsResponse {
  repeated string paths = 1;
}
